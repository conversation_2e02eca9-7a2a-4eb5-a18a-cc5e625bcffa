{"indexes": [{"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "savedArticles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "savedAt", "order": "DESCENDING"}]}, {"collectionGroup": "savedArticles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "savedAt", "order": "ASCENDING"}]}, {"collectionGroup": "articles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "articles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "articles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "authorId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "articles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "authorId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "articles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "authorId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}], "fieldOverrides": []}