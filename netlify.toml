# Netlify configuration for InfoNest SPA

[build]
  # Build command (if you're using <PERSON>lify's build process)
  command = "npm run build"
  # Directory to publish (Vite's default output directory)
  publish = "dist"

# Redirect all requests to index.html for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Optional: Security headers for better security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Optional: Cache static assets for better performance
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"